'use client';

import { useEffect, useRef, useState } from 'react';
import { ChevronDown, Loader2 } from 'lucide-react';
import { Chatroom } from '@/types';
import { useChatStore } from '@/stores/chatStore';
import { useUIStore } from '@/stores/uiStore';
import { ChatMessage } from './ChatMessage';
import { TypingIndicator } from './TypingIndicator';
import { ChatInput } from './ChatInput';
import { MessageFormData } from '@/lib/validations';
import { scrollToBottom, isAtBottom } from '@/lib/utils';

interface ChatInterfaceProps {
  chatroom: Chatroom;
}

export function ChatInterface({ chatroom }: ChatInterfaceProps) {
  const {
    messages,
    isMessagesLoading,
    isTyping,
    hasMoreMessages,
    sendMessage,
    loadMoreMessages,
  } = useChatStore();

  const { showToast } = useUIStore();
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const lastMessageCountRef = useRef(0);

  // Filter messages for current chatroom
  const chatroomMessages = messages.filter(msg => msg.chatroomId === chatroom.id);

  // WhatsApp-like auto-scroll behavior
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    // Only auto-scroll if:
    // 1. We're not loading more messages
    // 2. User is at the bottom or this is a new message from current user
    // 3. Or if it's the typing indicator
    if (!isLoadingMore) {
      const wasAtBottom = isAtBottom(container, 100);
      const messageCountChanged = chatroomMessages.length !== lastMessageCountRef.current;
      const isNewMessage = chatroomMessages.length > lastMessageCountRef.current;

      if (wasAtBottom || (isNewMessage && shouldAutoScroll) || isTyping) {
        setTimeout(() => {
          scrollToBottom(container, true);
        }, 50);
      }

      lastMessageCountRef.current = chatroomMessages.length;
    }
  }, [chatroomMessages.length, isTyping, isLoadingMore, shouldAutoScroll]);

  // Force scroll to bottom when switching chats
  useEffect(() => {
    if (messagesContainerRef.current && chatroom) {
      lastMessageCountRef.current = 0;
      setShouldAutoScroll(true);
      setTimeout(() => {
        scrollToBottom(messagesContainerRef.current!, false);
      }, 100);
    }
  }, [chatroom.id]);

  // WhatsApp-like scroll behavior management
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      // Clear any existing timeout
      clearTimeout(scrollTimeout);

      // Check if user is at bottom for auto-scroll behavior
      const atBottom = isAtBottom(container, 100);
      setShowScrollButton(!atBottom && chatroomMessages.length > 0);

      // Update auto-scroll preference based on user behavior
      if (!isLoadingMore) {
        setShouldAutoScroll(atBottom);
      }

      // Debounced infinite scroll check
      scrollTimeout = setTimeout(async () => {
        // Load more messages when near top
        if (container.scrollTop <= 50 && hasMoreMessages && !isLoadingMore && !isMessagesLoading) {
          setIsLoadingMore(true);

          // WhatsApp-style: Remember the first visible message
          const firstVisibleChild = container.firstElementChild as HTMLElement;
          const firstVisibleOffsetTop = firstVisibleChild?.offsetTop || 0;

          try {
            await loadMoreMessages();

            // Restore scroll position to maintain view
            requestAnimationFrame(() => {
              if (container && firstVisibleChild) {
                const newOffsetTop = firstVisibleChild.offsetTop;
                const scrollDiff = newOffsetTop - firstVisibleOffsetTop;
                container.scrollTop = container.scrollTop + scrollDiff;
              }
            });

          } catch (error) {
            showToast({
              type: 'error',
              message: 'Failed to load more messages',
            });
          } finally {
            setIsLoadingMore(false);
          }
        }
      }, 100); // 100ms debounce
    };

    container.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      container.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, [hasMoreMessages, isLoadingMore, isMessagesLoading, loadMoreMessages, showToast, chatroomMessages.length]);

  const handleSendMessage = async (data: MessageFormData) => {
    try {
      // Enable auto-scroll for new messages sent by user
      setShouldAutoScroll(true);
      await sendMessage(data);
    } catch (error) {
      throw error; // Let ChatInput handle the error display
    }
  };

  const handleScrollToBottom = () => {
    if (messagesContainerRef.current) {
      setShouldAutoScroll(true);
      scrollToBottom(messagesContainerRef.current, true);
    }
  };



  return (
    <div className="flex flex-col h-full bg-white">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto scroll-smooth"
      >
        {/* Load More Indicator */}
        {isLoadingMore && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="w-5 h-5 animate-spin text-gray-400 mr-2" />
            <span className="text-sm text-gray-500">Loading more messages...</span>
          </div>
        )}

        {/* No More Messages Indicator */}
        {!hasMoreMessages && chatroomMessages.length > 0 && (
          <div className="text-center py-4">
            <span className="text-xs text-gray-400 bg-gray-100 px-3 py-1 rounded-full">
              Beginning of conversation
            </span>
          </div>
        )}

        {/* Messages */}
        {chatroomMessages.length > 0 ? (
          <div className="space-y-0">
            {chatroomMessages.map((message, index) => (
              <ChatMessage
                key={message.id}
                message={message}
                isLast={index === chatroomMessages.length - 1}
              />
            ))}
          </div>
        ) : !isMessagesLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-md mx-auto p-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💬</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Start the conversation
              </h3>
              <p className="text-gray-600 mb-4">
                Send a message to begin chatting with Gemini AI. Ask questions, 
                share ideas, or just have a conversation!
              </p>
            </div>
          </div>
        ) : null}

        {/* Typing Indicator */}
        {isTyping && <TypingIndicator />}

        {/* Messages End Marker */}
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to Bottom Button */}
      {showScrollButton && (
        <div className="absolute bottom-20 right-4 z-10">
          <button
            onClick={handleScrollToBottom}
            className="p-2 bg-white border border-gray-300 rounded-full shadow-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="Scroll to bottom"
          >
            <ChevronDown className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      )}

      {/* Chat Input */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isMessagesLoading || isTyping}
      />
    </div>
  );
}
